#!/usr/bin/env python3
"""
Script genérico para ejecutar reportes ETL configurables.
<PERSON> datos desde S3, los procesa mediante consultas SQL y los exporta a archivos Parquet.

Uso:
    python run_reporte.py NOMBRE_REPORTE

Ejemplo:
    python run_reporte.py REPORTE_FULLCARGA
"""
import os
import sys
import configparser
import logging
import duckdb
import boto3
import shutil
from datetime import datetime, timedelta

def main():
    # Verificar argumentos
    if len(sys.argv) != 2:
        print("Uso: python run_reporte.py NOMBRE_REPORTE")
        print("Ejemplo: python run_reporte.py REPORTE_FULLCARGA")
        sys.exit(1)

    # Obtener nombre del reporte
    nombre_reporte = sys.argv[1]

    # Construir rutas
    config_path = os.path.join("CONFIGURACIONES", nombre_reporte, "config.ini")
    queries_dir = os.path.join("REPORTES", nombre_reporte, "queries")

    # Verificar que existan los directorios y archivos necesarios
    if not os.path.exists(config_path):
        print(f"Error: No se encontró el archivo de configuración en {config_path}")
        sys.exit(1)

    if not os.path.exists(queries_dir):
        print(f"Error: No se encontró el directorio de queries en {queries_dir}")
        sys.exit(1)

    # Leer configuración
    config = configparser.ConfigParser()
    config.read(config_path)

    # Configurar logging
    log_file = setup_logging(nombre_reporte, config)

    # Determinar rango de fechas
    fecha_inicio, fecha_fin = get_date_range(config)
    logging.info(f"Procesando datos para {nombre_reporte} desde {fecha_inicio} hasta {fecha_fin}")

    # Procesar cada fecha en el rango
    current_date = fecha_inicio
    while current_date <= fecha_fin:
        fecha_str = current_date.strftime('%Y-%m-%d')
        process_date(fecha_str, config, queries_dir, nombre_reporte)
        current_date += timedelta(days=1)

    logging.info("Proceso completado")

def setup_logging(nombre_reporte, config):
    """Configura el sistema de logging"""
    # Crear directorio de logs si no existe
    log_dir = config.get('general', 'log_dir', fallback='logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # Configurar el formato del log
    log_format = '%(asctime)s - %(levelname)s - %(message)s'

    # Crear un manejador para el archivo de log
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f'{log_dir}/{nombre_reporte}_{timestamp}.log'

    # Configurar el logger
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # También mostrar en consola
        ]
    )

    return log_file

def get_date_range(config):
    """Determina el rango de fechas a procesar según la configuración"""
    # Leer el valor del switch de rango
    usar_rango_fijo = config.getboolean('fecha', 'rango', fallback=False)

    if usar_rango_fijo:
        # Usar rango fijo
        return (
            datetime.strptime(config.get('fecha', 'rango_inicio'), '%Y-%m-%d').date(),
            datetime.strptime(config.get('fecha', 'rango_fin'), '%Y-%m-%d').date()
        )
    else:
        # Usar modo relativo
        dias_atras = int(config.get('fecha', 'dias_atras', fallback='1'))
        today = datetime.now().date()
        return (today - timedelta(days=dias_atras), today - timedelta(days=1))

def process_date(fecha, config, queries_dir, nombre_reporte):
    """Procesa los datos para una fecha específica"""
    logging.info(f"Procesando fecha: {fecha}")

    # Extraer componentes de la fecha
    fecha_parts = fecha.split('-')
    year, month, day = fecha_parts[0], fecha_parts[1], fecha_parts[2]

    # Obtener hora actual para el nombre del archivo
    hora_actual = datetime.now().strftime('%H%M%S')

    # Configurar conexión a DuckDB
    conn = duckdb.connect(database=':memory:')

    # Obtener credenciales activas de AWS CLI
    session = boto3.Session()
    credentials = session.get_credentials().get_frozen_credentials()

    # Cargar soporte para S3 en DuckDB
    conn.sql("INSTALL httpfs;")
    conn.sql("LOAD httpfs;")
    conn.sql("SET s3_region='us-east-1';")
    conn.sql("SET s3_use_ssl=true;")
    conn.sql("SET s3_url_style='path';")

    # Pasar credenciales explícitamente
    conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
    conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
    conn.sql(f"SET s3_session_token='{credentials.token}';")

    # Construir las rutas S3 para todos los orígenes definidos
    s3_paths = {}

    if config.has_section('s3_sources'):
        for source_name, source_value in config.items('s3_sources'):
            parts = [p.strip() for p in source_value.split(',')]
            if len(parts) >= 2:
                bucket = parts[0]
                prefix = parts[1]
                region = parts[2] if len(parts) > 2 else 'us-east-1'

                s3_paths[source_name] = {
                    'bucket': bucket,
                    'prefix': prefix,
                    'region': region,
                    'path': f's3://{bucket}/{prefix}/{year}/{month}/{day}/*'
                }
    else:
        # Compatibilidad con configuración antigua
        bucket = config.get('s3', 'bucket', fallback='')
        prefix = config.get('s3', 'prefix', fallback='')
        region = config.get('s3', 'region', fallback='us-east-1')

        s3_paths['main'] = {
            'bucket': bucket,
            'prefix': prefix,
            'region': region,
            'path': f's3://{bucket}/{prefix}/{year}/{month}/{day}/*'
        }

    # Construir la ruta de destino para el archivo Parquet
    output_base_dir = config.get('general', 'output_dir')
    output_dir = f'{output_base_dir}/{year}/{month}/{day}'
    output_file = f'{output_dir}/{nombre_reporte}_{fecha}_{hora_actual}.parquet'

    # Si existe la carpeta del día, eliminarla para evitar duplicados
    if os.path.exists(output_dir):
        logging.info(f"Eliminando carpeta existente para evitar duplicados: {output_dir}")
        shutil.rmtree(output_dir)

    # Crear directorios si no existen
    os.makedirs(output_dir, exist_ok=True)

    try:
        # Obtener lista de queries a ejecutar
        query_list = config.get('queries', 'query_list', fallback='').split(',')
        query_list = [q.strip() for q in query_list if q.strip()]

        if not query_list:
            logging.error("No se especificaron queries para ejecutar")
            return

        # Procesar cada query
        for query_name in query_list:
            query_file = os.path.join(queries_dir, f"{query_name}.sql")

            if not os.path.exists(query_file):
                logging.error(f"No se encontró el archivo de query: {query_file}")
                continue

            # Leer y parametrizar la query
            with open(query_file, 'r') as f:
                query_template = f.read()

            # Calcular el día siguiente para buscar transacciones reversadas
            fecha_dt = datetime.strptime(fecha, '%Y-%m-%d')
            next_date = fecha_dt + timedelta(days=1)
            next_year, next_month, next_day = next_date.strftime('%Y'), next_date.strftime('%m'), next_date.strftime('%d')

            # Crear un diccionario con todos los parámetros para formatear la query
            format_params = {
                'year': year,
                'month': month,
                'day': day,
                'next_year': next_year,
                'next_month': next_month,
                'next_day': next_day,
                'fecha_inicio': fecha,
                'fecha_fin': fecha
            }

            # Agregar todos los paths S3 como parámetros
            for source_name, source_info in s3_paths.items():
                format_params[f'{source_name}_bucket'] = source_info['bucket']
                format_params[f'{source_name}_prefix'] = source_info['prefix']
                format_params[f'{source_name}_region'] = source_info['region']
                format_params[f'{source_name}_path'] = source_info['path']

            # Reemplazar parámetros en la query
            # Primero, escapar las llaves que no son parte del formato
            query_template = query_template.replace('{{', '{{{{').replace('}}', '}}}}')
            query = query_template.format(**format_params)

            # Ejecutar la query
            logging.info(f"Ejecutando query: {query_name}")
            df = conn.sql(query).fetchdf()

            logging.info(f"Se encontraron {len(df)} registros para procesar")

            if len(df) == 0:
                logging.warning(f"No hay registros para procesar en la fecha {fecha}")
                continue

            # Exportar a Parquet
            conn.sql(f"""
                CREATE TABLE temp_data AS SELECT * FROM df
            """)

            conn.sql(f"""
                COPY (SELECT * FROM temp_data)
                TO '{output_file}' (FORMAT PARQUET)
            """)

            logging.info(f"Resultados exportados a {output_file}")

            # Mostrar muestra de datos
            muestra_df = conn.sql("SELECT * FROM temp_data LIMIT 5").fetchdf()
            logging.info(f"Muestra de datos procesados:\n{muestra_df}")

            # Limpiar tabla temporal
            conn.sql("DROP TABLE IF EXISTS temp_data")

    except Exception as e:
        logging.error(f"Error al procesar la fecha {fecha}: {str(e)}")

if __name__ == "__main__":
    main()

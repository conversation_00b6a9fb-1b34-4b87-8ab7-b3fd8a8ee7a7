-- Consulta para procesar transacciones de FULLCARGA
--
-- Este reporte utiliza dos fuentes de datos:
-- 1. cashin_prod: Tabla CASHIN_PROD - Para identificar transacciones reversadas
-- 2. main: Tabla PRE_CONCILIACION_PROD - Para obtener transacciones exitosas
--
-- Paso 1: Obtener transacciones con estado REVERSAL para excluirlas
WITH transacciones_reversadas AS (
    SELECT
        transaction_id
    FROM
        -- Buscar en la carpeta del día actual
        read_parquet('{cashin_prod_path}')
    WHERE
        status = 'REVERSAL' AND emisor IN ('FULLCARGA_CASHIN', 'FULLCARGA_CASHOUT')
),

-- Obtener transacciones exitosas
datos_transacciones AS (
    SELECT
        id_transaccion,
        recaudador,
        created_at,
        msisdn,
        status,
        trama_entrada
    FROM
        read_parquet('{main_path}')
    WHERE
        created_at::DATE = '{fecha_inicio}'::DATE
        AND recaudador IN ('FULLCARGA_CASHIN', 'FULLCARGA_CASHOUT')
        AND status = 'SUCCESSFUL'
),

-- Extraer información de trama_entrada
datos_procesados AS (
    SELECT
        id_transaccion,
        recaudador,
        created_at,
        msisdn,
        status,
        -- Extraer ext_trx_id de trama_entrada
        CASE
            WHEN recaudador = 'FULLCARGA_CASHOUT' THEN
                REGEXP_EXTRACT(trama_entrada, 'external_transaction_id.: .([0-9]+)', 1)
            WHEN recaudador = 'FULLCARGA_CASHIN' THEN
                REGEXP_EXTRACT(trama_entrada, 'external_id.: .([0-9]+)', 1)
            ELSE
                id_transaccion
        END AS ext_trx_id,
        -- Extraer amount de trama_entrada
        REGEXP_EXTRACT(trama_entrada, 'amount.: .([0-9]+[.][0-9]+)', 1) AS amount
    FROM
        datos_transacciones
)

-- Seleccionar y formatear campos finales
SELECT
    -- ID de transacción
    CASE
        WHEN id_transaccion IS NULL OR id_transaccion = '' OR id_transaccion = '--' THEN
            'TRX-' || EXTRACT(EPOCH FROM created_at)::VARCHAR
        ELSE id_transaccion
    END AS fin_trx_id,
    ext_trx_id,
    created_at AS datetime,
    -- Tipo de transacción
    CASE
        WHEN recaudador = 'FULLCARGA_CASHIN' THEN 'CASH_IN'
        WHEN recaudador = 'FULLCARGA_CASHOUT' THEN 'CASH_OUT_ATM'
        ELSE 'UNKNOWN'
    END AS trx_type,
    -- Usar valor hardcodeado para from_msisdn para mantener compatibilidad con el SP original
    '51969752322' AS from_msisdn,
    -- Email (msisdn + @fullcarga)
    msisdn || '@fullcarga' AS email,
    amount
FROM
    datos_procesados
-- Excluir transacciones reversadas
WHERE id_transaccion NOT IN (SELECT transaction_id FROM transacciones_reversadas)
ORDER BY
    datetime

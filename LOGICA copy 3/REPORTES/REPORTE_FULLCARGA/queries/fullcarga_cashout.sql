-- Consulta para procesar solo transacciones FULLCARGA_CASHOUT

-- Obtener datos crudos
WITH datos_crudos AS (
    SELECT
        id_transaccion,
        recaudador,
        trama_entrada,
        created_at,
        msisdn
    FROM
        read_parquet('{main_path}')
    WHERE
        created_at::DATE = '{fecha_inicio}'::DATE
        AND recaudador = 'FULLCARGA_CASHOUT'
        AND status = 'SUCCESSFUL'
)

-- Extraer campos JSON y transformar datos
SELECT
    id_transaccion AS fin_trx_id,
    -- Extraer campos JSON específicos para CASHOUT
    CASE
        WHEN TRY_CAST(trama_entrada AS JSON) IS NOT NULL
        THEN JSON_EXTRACT_STRING(CAST(trama_entrada AS JSON), '$.external_transaction_id')
        ELSE regexp_extract(trama_entrada, 'external_transaction_id["\']*\\s*:\\s*["\']*([^,"\' ]+)', 1)
    END AS ext_trx_id,

    created_at AS datetime,
    'CASH_OUT_ATM' AS trx_type,
    '51969752322' AS from_msisdn,
    msisdn AS to_username,

    CASE
        WHEN TRY_CAST(trama_entrada AS JSON) IS NOT NULL
        THEN JSON_EXTRACT_STRING(CAST(trama_entrada AS JSON), '$.amount')
        ELSE regexp_extract(trama_entrada, 'amount["\']*\\s*:\\s*["\']*([^,"\' ]+)', 1)
    END AS amount
FROM
    datos_crudos
ORDER BY
    datetime

# Reporte de Conciliación FULLCARGA

Este reporte procesa transacciones de FULLCARGA para generar un archivo de conciliación.

## Fuentes de datos

El reporte utiliza dos fuentes de datos principales:

1. **PRE_CONCILIACION_PROD** (variable `main` en config.ini)
   - Contiene las transacciones exitosas con `status = 'SUCCESSFUL'`
   - Se accede mediante la variable `{main_path}` en el SQL

2. **CASHIN_PROD** (variable `cashin_prod` en config.ini)
   - Contiene información sobre transacciones reversadas con `status = 'REVERSAL'`
   - Se accede mediante la variable `{cashin_prod_path}` en el SQL

## Flujo de procesamiento

El reporte sigue estos pasos:

1. **Identificar transacciones reversadas**:
   - Consulta la tabla CASHIN_PROD para encontrar transacciones con estado 'REVERSAL'
   - Guarda los IDs de estas transacciones para excluirlas del resultado final

2. **Obtener transacciones exitosas**:
   - Consulta la tabla PRE_CONCILIACION_PROD para obtener transacciones con estado 'SUCCESSFUL'
   - Filtra por recaudador ('FULLCARGA_CASHIN', 'FULLCARGA_CASHOUT')

3. **Extraer información adicional**:
   - Extrae datos como `ext_trx_id` y `amount` del campo JSON `trama_entrada`
   - Utiliza expresiones regulares para extraer estos valores

4. **Formatear resultados finales**:
   - Genera campos adicionales como `fin_trx_id`, `trx_type`, etc.
   - Excluye las transacciones que fueron reversadas

5. **Exportar a Parquet**:
   - Guarda los resultados en un archivo Parquet en la carpeta configurada

## Variables disponibles en SQL

### Variables de fecha:
- `{year}`, `{month}`, `{day}`: Componentes de la fecha actual
- `{next_year}`, `{next_month}`, `{next_day}`: Componentes del día siguiente
- `{fecha_inicio}`, `{fecha_fin}`: Fechas en formato YYYY-MM-DD

### Variables de fuentes de datos:
Para cada fuente definida en `[s3_sources]` del archivo config.ini, se generan:

- `{nombre_fuente}_bucket`: El bucket de S3
- `{nombre_fuente}_prefix`: El prefijo dentro del bucket
- `{nombre_fuente}_region`: La región de AWS
- `{nombre_fuente}_path`: La ruta completa (s3://{bucket}/{prefix}/{year}/{month}/{day}/*)

### Ejemplo:

Para la fuente `cashin_prod`, se generan:
- `{cashin_prod_bucket}`: prd-datalake-bronze-zone-637423440311
- `{cashin_prod_prefix}`: DBMDWPROD/CASHIN_PROD
- `{cashin_prod_path}`: s3://prd-datalake-bronze-zone-637423440311/DBMDWPROD/CASHIN_PROD/2025/05/09/*

## Configuración

La configuración del reporte se encuentra en `CONFIGURACIONES/REPORTE_FULLCARGA/config.ini`.

### Sección [general]
- `output_dir`: Directorio base para los archivos de salida
- `log_dir`: Directorio para los archivos de log

### Sección [fecha]
- `rango`: Si es `true`, usa fechas fijas; si es `false`, usa días hacia atrás
- `dias_atras`: Número de días hacia atrás (si `rango = false`)
- `rango_inicio`, `rango_fin`: Fechas fijas (si `rango = true`)

### Sección [s3_sources]
Define las fuentes de datos en formato: `nombre = bucket, prefix, region`

### Sección [queries]
- `query_list`: Lista de consultas SQL a ejecutar (sin extensión .sql)

## Ejecución

Para ejecutar el reporte:

```bash
cd LOGICA
python run_reporte.py REPORTE_FULLCARGA
```

## Archivos SQL

- **fullcarga_all.sql**: Procesa tanto transacciones CASHIN como CASHOUT
- **fullcarga_cashin.sql**: Procesa solo transacciones CASHIN
- **fullcarga_cashout.sql**: Procesa solo transacciones CASHOUT

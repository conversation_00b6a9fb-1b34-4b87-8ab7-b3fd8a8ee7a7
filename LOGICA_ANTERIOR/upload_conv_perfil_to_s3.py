#!/usr/bin/env python3
"""
Script temporal para convertir conv_perfil.csv a Parquet y subirlo a S3
Destino: s3://prd-datalake-silver-catalog-zone-637423440311/LOGS_USUARIOS/
"""

import pandas as pd
import boto3
import os
import tempfile
from datetime import datetime
from pathlib import Path

def main():
    # Rutas
    csv_file = "REPORTES/REPORTE_LOG_USUARIO/conv_perfil.csv"
    s3_bucket = "prd-datalake-silver-catalog-zone-637423440311"
    s3_key = "LOGS_USUARIOS/conv_perfil.parquet"
    
    try:
        print(f"📊 Iniciando conversión de {csv_file} a Parquet...")
        
        # Verificar que el archivo CSV existe
        if not os.path.exists(csv_file):
            print(f"❌ Error: Archivo CSV no encontrado: {csv_file}")
            return False
            
        # Leer CSV
        print(f"📖 Leyendo archivo CSV...")
        df = pd.read_csv(csv_file)
        print(f"✅ CSV leído exitosamente: {len(df):,} registros")
        print(f"📋 Columnas: {list(df.columns)}")
        
        # Mostrar información del DataFrame
        print(f"\n📊 Información del DataFrame:")
        print(f"   - Filas: {len(df):,}")
        print(f"   - Columnas: {len(df.columns)}")
        print(f"   - Tipos de datos:")
        for col in df.columns:
            print(f"     • {col}: {df[col].dtype}")
        
        # Crear archivo temporal Parquet
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_file:
            temp_parquet_path = temp_file.name
            
        print(f"💾 Convirtiendo a Parquet...")
        df.to_parquet(temp_parquet_path, index=False, engine='pyarrow')
        
        # Verificar tamaño del archivo
        file_size = os.path.getsize(temp_parquet_path)
        print(f"✅ Archivo Parquet creado: {file_size:,} bytes")
        
        # Subir a S3
        print(f"📤 Subiendo a S3: s3://{s3_bucket}/{s3_key}")
        s3_client = boto3.client('s3')
        
        # Agregar metadatos
        metadata = {
            'source_file': csv_file,
            'conversion_date': datetime.now().isoformat(),
            'records_count': str(len(df)),
            'columns_count': str(len(df.columns))
        }
        
        s3_client.upload_file(
            temp_parquet_path,
            s3_bucket,
            s3_key,
            ExtraArgs={
                'ContentType': 'application/octet-stream',
                'Metadata': metadata
            }
        )
        
        # Verificar que se subió correctamente
        try:
            response = s3_client.head_object(Bucket=s3_bucket, Key=s3_key)
            s3_size = response['ContentLength']
            print(f"✅ Archivo subido exitosamente a S3")
            print(f"📊 Tamaño en S3: {s3_size:,} bytes")
            print(f"📍 Ubicación: s3://{s3_bucket}/{s3_key}")
        except Exception as verify_error:
            print(f"⚠️ Advertencia: No se pudo verificar la subida: {verify_error}")
        
        # Limpiar archivo temporal
        try:
            os.remove(temp_parquet_path)
            print(f"🧹 Archivo temporal eliminado")
        except Exception as cleanup_error:
            print(f"⚠️ Advertencia: No se pudo eliminar archivo temporal: {cleanup_error}")
        
        print(f"\n🎉 ¡Conversión y subida completadas exitosamente!")
        return True
        
    except Exception as e:
        print(f"❌ Error durante el proceso: {e}")
        # Limpiar archivo temporal en caso de error
        try:
            if 'temp_parquet_path' in locals() and os.path.exists(temp_parquet_path):
                os.remove(temp_parquet_path)
        except:
            pass
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

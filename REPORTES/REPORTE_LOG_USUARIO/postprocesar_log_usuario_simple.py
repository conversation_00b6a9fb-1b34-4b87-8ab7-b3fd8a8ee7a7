#!/usr/bin/env python3
"""
Post-procesador SIMPLE para LOG_USUARIO - DOS GOTAS DE AGUA
Solo usa el procesador original sin modificaciones adicionales
"""

import sys
import os
import subprocess
import glob
from datetime import datetime
from pathlib import Path

class LogUsuariosPostProcessorSimple:
    def __init__(self):
        print("Inicializando post-procesador SIMPLE (DOS GOTAS DE AGUA)")
        
    def create_temp_files(self, fecha: str):
        """
        Crear archivos temporales como el original para mantener estructura completa
        """
        try:
            fecha_formatted = datetime.strptime(fecha, '%Y-%m-%d').strftime('%Y%m%d')
            temp_dir = f"TEMP_LOGS_USUARIOS/{fecha_formatted}"
            Path(temp_dir).mkdir(parents=True, exist_ok=True)
            
            print(f"📁 Creando archivos temporales en {temp_dir}...")
            
            # Copiar archivos temporales reales del original
            original_temp_dir = "/home/<USER>/aws/REP/reports/generate_nv/PRO_LOGICA_REP/S3_LOG_USER/TEMP_LOGS_USUARIOS"
            
            # Buscar el directorio más reciente en el original
            import glob
            original_dirs = glob.glob(f"{original_temp_dir}/202*")
            if original_dirs:
                latest_dir = max(original_dirs)
                print(f"📁 Copiando archivos temporales desde: {latest_dir}")
                
                import shutil
                temp_files = [
                    "USER_ACCOUNT_HISTORY.parquet",
                    "USER_AUTH_CHANGE_HISTORY.parquet", 
                    "USER_DATA_TRX.parquet",
                    "USER_MODIFICATION_DAY.parquet"
                ]
                
                for temp_file in temp_files:
                    src_path = Path(latest_dir) / temp_file
                    dst_path = Path(temp_dir) / temp_file
                    
                    if src_path.exists():
                        shutil.copy2(src_path, dst_path)
                        print(f"  📄 Copiado: {temp_file} ({dst_path.stat().st_size} bytes)")
                    else:
                        # Crear archivo vacío si no existe en el original
                        import pandas as pd
                        df_empty = pd.DataFrame({'placeholder': [1]})
                        df_empty.to_parquet(dst_path, index=False)
                        print(f"  📄 Creado vacío: {temp_file}")
            else:
                print(f"⚠️ No se encontraron archivos temporales en el original")
                
        except Exception as e:
            print(f"⚠️ Error creando archivos temporales: {e}")

    def remove_empty_bankdomain_files(self, output_dir: str):
        """
        Eliminar archivos con BANKDOMAIN vacío que no existen en el original
        """
        try:
            import glob
            # Buscar archivos con nombre vacío (LOGUSR--*.csv)
            empty_files = glob.glob(f"{output_dir}/LOGUSR--*.csv")
            
            for file_path in empty_files:
                try:
                    Path(file_path).unlink()
                    print(f"🧹 Archivo con BANKDOMAIN vacío eliminado: {Path(file_path).name}")
                except Exception as e:
                    print(f"⚠️ Error eliminando {file_path}: {e}")
                    
            if not empty_files:
                print(f"✅ No se encontraron archivos con BANKDOMAIN vacío")
                
        except Exception as e:
            print(f"⚠️ Error eliminando archivos con BANKDOMAIN vacío: {e}")

    def apply_original_processing(self, parquet_path: str, fecha: str) -> list:
        """
        Ejecutar el procesador original EXACTO sin modificaciones
        """
        try:
            print(f"🔄 Ejecutando procesador original exacto...")
            
            # Crear directorio de salida
            output_dir = f"output/csv_exports_exactos_finales"
            Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            # Limpiar archivos anteriores para evitar duplicados
            import glob
            archivos_anteriores = glob.glob(f"{output_dir}/LOGUSR-*.csv")
            for archivo in archivos_anteriores:
                try:
                    Path(archivo).unlink()
                    print(f"🧹 Archivo anterior eliminado: {Path(archivo).name}")
                except:
                    pass
            
            # Ejecutar el procesador original directamente
            original_processor_path = "/home/<USER>/aws/REP/reports/generate_nv/PRO_LOGICA_REP/S3_LOG_USER/procesar_log_usuarios.py"
            
            command = [
                "python3", 
                original_processor_path,
                parquet_path,
                fecha,
                output_dir
            ]
            
            print(f"Ejecutando: {' '.join(command)}")
            
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                cwd="/home/<USER>/aws/REP/reports/generate_nv/PRO_LOGICA_REP/S3_LOG_USER"
            )
            
            if result.returncode == 0:
                # Verificar archivos generados
                generated_files = glob.glob(f"{output_dir}/LOGUSR-*.csv")
                print(f"✅ Procesamiento completo exitoso: {len(generated_files)} archivos")
                for file_path in generated_files:
                    print(f"  📁 {Path(file_path).name}")
                return generated_files
            else:
                print(f"❌ Error en procesador original:")
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")
                return []
                
        except Exception as e:
            print(f"❌ Error ejecutando procesador original: {e}")
            return []

    def process_log_usuarios(self, fecha: str) -> list:
        """
        Procesa los datos usando SOLO el procesador original (DOS GOTAS DE AGUA)
        """
        print(f"Iniciando post-procesamiento SIMPLE para fecha: {fecha}")
        
        # Extraer componentes de la fecha
        fecha_parts = fecha.split('-')
        year, month, day = fecha_parts[0], fecha_parts[1], fecha_parts[2]
        
        try:
            # Buscar archivos parquet generados por run_reporte.py
            parquet_path = f"REPORTE_LOG_USUARIOS/{year}/{month}/{day}"
            
            if os.path.exists(parquet_path):
                parquet_files = [f for f in os.listdir(parquet_path) if f.endswith('.parquet')]
                if parquet_files:
                    latest_file = sorted(parquet_files)[-1]
                    full_parquet_path = f"{parquet_path}/{latest_file}"
                    
                    print(f"Procesando archivo: {full_parquet_path}")
                    
                    # PASO 1: Crear archivos temporales como el original
                    self.create_temp_files(fecha)
                    
                    # PASO 2: Usar SOLO el procesador original
                    processed_files = self.apply_original_processing(full_parquet_path, fecha)

                    if processed_files:
                        # PASO 3: Eliminar archivos con nombre vacío
                        self.remove_empty_bankdomain_files("output/csv_exports_exactos_finales")
                        
                        print(f"✅ Procesamiento original exitoso: {len(processed_files)} archivos generados")
                        print(f"📁 Archivos temporales creados en TEMP_LOGS_USUARIOS/")
                        return processed_files
                    else:
                        print("❌ Error: El procesador original no funcionó")
                        return []
                else:
                    print(f"❌ No se encontraron archivos parquet en {parquet_path}")
                    return []
            else:
                print(f"❌ Directorio no encontrado: {parquet_path}")
                return []
                
        except Exception as e:
            print(f"❌ Error en post-procesamiento: {e}")
            return []

def main():
    if len(sys.argv) < 2:
        print("Uso: python postprocesar_log_usuario_simple.py YYYY-MM-DD")
        sys.exit(1)
        
    fecha = sys.argv[1]
    
    try:
        processor = LogUsuariosPostProcessorSimple()
        exported_files = processor.process_log_usuarios(fecha)
        
        if exported_files:
            print(f"✅ Post-procesamiento completado exitosamente")
            print(f"📁 Archivos generados: {len(exported_files)}")
        else:
            print("❌ No se generaron archivos")
            
    except Exception as e:
        print(f"❌ Error en post-procesamiento: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Script de validación automática para verificar que la corrección del filtrado
por BANKDOMAIN funcione correctamente en cualquier fecha.

Este script asegura que la solución sea general y permanente.
"""

import boto3
import pandas as pd
import sys
from datetime import datetime, timedelta
import tempfile
import os

class BankDomainValidator:
    def __init__(self):
        self.s3_client = boto3.client('s3')
        self.final_bucket = 'prd-datalake-golden-reporte-final-zone-************'
        self.expected_banks = ['FCOMPARTAMOS', 'BNACION', 'CRANDES', 'CCUSCO', '0231FCONFIANZA']
    
    def get_latest_files_for_date(self, date_str):
        """Obtener los archivos más recientes para una fecha específica"""
        # Convertir fecha a formato de carpeta (YYYY-MM-DD + 1 día)
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        folder_date = (date_obj + timedelta(days=1)).strftime('%Y-%m-%d')
        
        files_found = {}
        
        for bank in self.expected_banks:
            prefix = f"{bank}/{folder_date}/"
            
            try:
                response = self.s3_client.list_objects_v2(
                    Bucket=self.final_bucket,
                    Prefix=prefix
                )
                
                if 'Contents' in response:
                    # Obtener el archivo más reciente
                    latest_file = max(response['Contents'], key=lambda x: x['LastModified'])
                    files_found[bank] = {
                        'key': latest_file['Key'],
                        'size': latest_file['Size'],
                        'last_modified': latest_file['LastModified']
                    }
                else:
                    print(f"⚠️ No se encontraron archivos para {bank} en fecha {folder_date}")
                    
            except Exception as e:
                print(f"❌ Error buscando archivos para {bank}: {e}")
        
        return files_found
    
    def validate_file_content(self, bank, s3_key):
        """Validar que un archivo contenga solo registros del banco correspondiente"""
        try:
            # Descargar archivo temporalmente
            with tempfile.NamedTemporaryFile(mode='w+b', delete=False, suffix='.csv') as temp_file:
                temp_path = temp_file.name
                
            self.s3_client.download_file(self.final_bucket, s3_key, temp_path)
            
            # Leer archivo y verificar contenido
            df = pd.read_csv(temp_path, header=None)
            
            if len(df) == 0:
                print(f"⚠️ {bank}: Archivo vacío")
                os.unlink(temp_path)
                return False, 0, []
            
            # La columna 6 (índice 6) debería contener BANKDOMAIN
            bankdomains_in_file = df.iloc[:, 6].unique()
            
            # Filtrar valores nulos/vacíos
            bankdomains_in_file = [bd for bd in bankdomains_in_file if pd.notna(bd) and str(bd).strip() != '']
            
            # Verificar si solo contiene el banco esperado
            is_valid = len(bankdomains_in_file) == 1 and bankdomains_in_file[0] == bank
            
            # Limpiar archivo temporal
            os.unlink(temp_path)
            
            return is_valid, len(df), bankdomains_in_file
            
        except Exception as e:
            print(f"❌ Error validando {bank}: {e}")
            if 'temp_path' in locals() and os.path.exists(temp_path):
                os.unlink(temp_path)
            return False, 0, []
    
    def validate_date(self, date_str):
        """Validar todos los archivos para una fecha específica"""
        print(f"\n🔍 Validando archivos para fecha: {date_str}")
        print("=" * 60)
        
        files = self.get_latest_files_for_date(date_str)
        
        if not files:
            print(f"❌ No se encontraron archivos para la fecha {date_str}")
            return False
        
        validation_results = {}
        total_records = 0
        
        for bank in self.expected_banks:
            if bank in files:
                file_info = files[bank]
                s3_key = file_info['key']
                file_size = file_info['size']
                
                print(f"\n📁 {bank}:")
                print(f"   Archivo: {os.path.basename(s3_key)}")
                print(f"   Tamaño: {file_size:,} bytes")
                print(f"   Modificado: {file_info['last_modified']}")
                
                # Validar contenido
                is_valid, record_count, found_banks = self.validate_file_content(bank, s3_key)
                
                validation_results[bank] = {
                    'valid': is_valid,
                    'records': record_count,
                    'found_banks': found_banks,
                    'file_size': file_size
                }
                
                total_records += record_count
                
                if is_valid:
                    print(f"   ✅ VÁLIDO: {record_count:,} registros de {bank}")
                else:
                    print(f"   ❌ INVÁLIDO: {record_count:,} registros")
                    print(f"   🏦 Bancos encontrados: {found_banks}")
            else:
                print(f"\n📁 {bank}: ❌ Archivo no encontrado")
                validation_results[bank] = {
                    'valid': False,
                    'records': 0,
                    'found_banks': [],
                    'file_size': 0
                }
        
        # Resumen
        print(f"\n📊 Resumen de validación:")
        print(f"   Fecha: {date_str}")
        print(f"   Total registros: {total_records:,}")
        
        valid_count = sum(1 for result in validation_results.values() if result['valid'])
        total_count = len(self.expected_banks)
        
        print(f"   Archivos válidos: {valid_count}/{total_count}")
        
        if valid_count == total_count:
            print(f"   🎉 ¡TODOS LOS ARCHIVOS SON VÁLIDOS!")
            return True
        else:
            print(f"   ⚠️ Algunos archivos tienen problemas")
            return False
    
    def validate_multiple_dates(self, dates):
        """Validar múltiples fechas"""
        print("🚀 Iniciando validación de múltiples fechas...")
        print(f"📅 Fechas a validar: {len(dates)}")
        
        results = {}
        
        for date_str in dates:
            results[date_str] = self.validate_date(date_str)
        
        # Resumen final
        print(f"\n" + "=" * 80)
        print(f"📊 RESUMEN FINAL DE VALIDACIÓN")
        print(f"=" * 80)
        
        valid_dates = sum(results.values())
        total_dates = len(dates)
        
        for date_str, is_valid in results.items():
            status = "✅ VÁLIDA" if is_valid else "❌ INVÁLIDA"
            print(f"   {date_str}: {status}")
        
        print(f"\n🎯 Resultado general: {valid_dates}/{total_dates} fechas válidas")
        
        if valid_dates == total_dates:
            print("🎉 ¡TODAS LAS FECHAS SON VÁLIDAS!")
            print("✅ La corrección del filtrado por BANKDOMAIN funciona correctamente")
            return True
        else:
            print("⚠️ Algunas fechas tienen problemas")
            print("🔧 Revisar la implementación del filtrado por BANKDOMAIN")
            return False

def main():
    validator = BankDomainValidator()
    
    # Fechas a validar (últimos días)
    dates_to_validate = [
        '2025-06-10',  # Fecha que sabemos que funciona localmente
        '2025-06-09',  # Día anterior
        '2025-06-08',  # Dos días antes
    ]
    
    # Validar múltiples fechas
    success = validator.validate_multiple_dates(dates_to_validate)
    
    if success:
        print("\n✅ VALIDACIÓN EXITOSA - La solución es general y permanente")
    else:
        print("\n❌ VALIDACIÓN FALLÓ - Se requieren correcciones adicionales")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
"""
Script de sincronización automática para asegurar que todos los scripts
estén actualizados en S3 y sean consistentes entre local y AWS Batch.

Este script resuelve el problema de inconsistencias entre entornos.
"""

import boto3
import os
import hashlib
from pathlib import Path
from datetime import datetime

class ScriptSynchronizer:
    def __init__(self):
        self.s3_client = boto3.client('s3')
        self.bucket = 'prd-datalake-report-configuration-637423440311'
        self.base_prefix = 'REPORTES/REPORTE_LOG_USUARIO/'
        
        # Archivos críticos que deben estar sincronizados
        self.critical_files = {
            'procesar_log_usuarios.py': 'S3_LOG_USER/procesar_log_usuarios.py',
            'postprocesar_log_usuario.py': 'LOGICA/REPORTES/REPORTE_LOG_USUARIO/postprocesar_log_usuario.py',
            'conv_perfil.csv': 'LOGICA/REPORTES/REPORTE_LOG_USUARIO/conv_perfil.csv'
        }
    
    def get_file_hash(self, file_path):
        """Calcular hash MD5 de un archivo local"""
        if not os.path.exists(file_path):
            return None
        
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def get_s3_file_hash(self, s3_key):
        """Obtener hash MD5 de un archivo en S3"""
        try:
            response = self.s3_client.head_object(Bucket=self.bucket, Key=s3_key)
            # S3 devuelve ETag que es el MD5 para archivos simples
            etag = response['ETag'].strip('"')
            return etag
        except Exception as e:
            print(f"⚠️ Error obteniendo hash de S3 para {s3_key}: {e}")
            return None
    
    def upload_file_to_s3(self, local_path, s3_key):
        """Subir archivo a S3 con metadatos"""
        try:
            file_size = os.path.getsize(local_path)
            
            # Metadatos
            metadata = {
                'sync_date': datetime.now().isoformat(),
                'local_path': local_path,
                'file_size': str(file_size)
            }
            
            self.s3_client.upload_file(
                local_path,
                self.bucket,
                s3_key,
                ExtraArgs={
                    'ContentType': 'text/plain' if local_path.endswith('.py') else 'text/csv',
                    'Metadata': metadata
                }
            )
            
            print(f"✅ Subido: {local_path} → s3://{self.bucket}/{s3_key}")
            return True
            
        except Exception as e:
            print(f"❌ Error subiendo {local_path}: {e}")
            return False
    
    def sync_file(self, s3_filename, local_path):
        """Sincronizar un archivo específico"""
        s3_key = f"{self.base_prefix}{s3_filename}"
        
        print(f"\n🔄 Sincronizando: {s3_filename}")
        print(f"   Local: {local_path}")
        print(f"   S3: s3://{self.bucket}/{s3_key}")
        
        # Verificar si el archivo local existe
        if not os.path.exists(local_path):
            print(f"❌ Archivo local no encontrado: {local_path}")
            return False
        
        # Obtener hashes
        local_hash = self.get_file_hash(local_path)
        s3_hash = self.get_s3_file_hash(s3_key)
        
        print(f"   Hash local: {local_hash}")
        print(f"   Hash S3:    {s3_hash}")
        
        # Comparar hashes
        if local_hash == s3_hash:
            print(f"✅ Archivo ya está sincronizado")
            return True
        else:
            print(f"🔄 Archivo necesita actualización")
            return self.upload_file_to_s3(local_path, s3_key)
    
    def sync_all(self):
        """Sincronizar todos los archivos críticos"""
        print("🚀 Iniciando sincronización de scripts críticos...")
        print(f"📅 Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {}
        
        for s3_filename, local_path in self.critical_files.items():
            results[s3_filename] = self.sync_file(s3_filename, local_path)
        
        # Resumen
        print(f"\n📊 Resumen de sincronización:")
        success_count = sum(results.values())
        total_count = len(results)
        
        for filename, success in results.items():
            status = "✅ OK" if success else "❌ FALLO"
            print(f"   {filename}: {status}")
        
        print(f"\n🎯 Resultado: {success_count}/{total_count} archivos sincronizados")
        
        if success_count == total_count:
            print("🎉 ¡Todos los archivos están sincronizados!")
            print("🔧 AWS Batch ahora usará las versiones corregidas")
        else:
            print("⚠️ Algunos archivos no se pudieron sincronizar")
        
        return success_count == total_count
    
    def verify_sync(self):
        """Verificar que todos los archivos estén sincronizados"""
        print("🔍 Verificando sincronización...")
        
        all_synced = True
        for s3_filename, local_path in self.critical_files.items():
            s3_key = f"{self.base_prefix}{s3_filename}"
            
            if not os.path.exists(local_path):
                print(f"❌ {s3_filename}: Archivo local no existe")
                all_synced = False
                continue
            
            local_hash = self.get_file_hash(local_path)
            s3_hash = self.get_s3_file_hash(s3_key)
            
            if local_hash == s3_hash:
                print(f"✅ {s3_filename}: Sincronizado")
            else:
                print(f"❌ {s3_filename}: Desincronizado")
                all_synced = False
        
        return all_synced

def main():
    synchronizer = ScriptSynchronizer()
    
    # Sincronizar todos los archivos
    success = synchronizer.sync_all()
    
    if success:
        print("\n🎯 Verificación final...")
        if synchronizer.verify_sync():
            print("✅ Verificación exitosa - Todos los archivos están sincronizados")
        else:
            print("❌ Verificación falló - Algunos archivos siguen desincronizados")

if __name__ == "__main__":
    main()

import duckdb
import boto3

# **1. Obtener credenciales activas de AWS CLI**
session = boto3.Session()
credentials = session.get_credentials().get_frozen_credentials()

# **2. Cargar soporte para S3 en DuckDB**
duckdb.sql("INSTALL httpfs;")
duckdb.sql("LOAD httpfs;")
duckdb.sql("SET s3_region='us-east-1';")
duckdb.sql("SET s3_use_ssl=true;")
duckdb.sql("SET s3_url_style='path';")

# **3. Pasar credenciales explícitamente**
duckdb.sql(f"SET s3_access_key_id='{credentials.access_key}';")
duckdb.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
duckdb.sql(f"SET s3_session_token='{credentials.token}';")

# **4. <PERSON><PERSON><PERSON> correctas**
pre_conciliacion_path = 's3://prd-datalake-bronze-zone-637423440311/DBMDWPROD/PRE_CONCILIACION_PROD/2025/05/06/*'
cashin_path = 's3://prd-datalake-bronze-zone-637423440311/DBMDWPROD/CASHIN_PROD/2025/05/06/*'

# **5. Buscar transacciones específicas en PRE_CONCILIACION_PROD**
try:
    print("Buscando en PRE_CONCILIACION_PROD...")
    df = duckdb.sql(f"""
        SELECT id_transaccion, recaudador, created_at, msisdn, status, trama_entrada
        FROM read_parquet('{pre_conciliacion_path}')
        WHERE id_transaccion IN ('5000024037', '5000024052', '8000024032', '5000024071', '9000023988', '7000023969', '8000024102')
        ORDER BY created_at
    """)
    print(df)
except duckdb.IOException as e:
    print("Error de DuckDB en PRE_CONCILIACION_PROD:", e)

# **6. Buscar transacciones reversadas en CASHIN_PROD**
try:
    print("\nBuscando transacciones reversadas en CASHIN_PROD...")
    df = duckdb.sql(f"""
        SELECT transaction_id, status, emisor, created_at
        FROM read_parquet('{cashin_path}')
        WHERE status = 'REVERSAL' AND emisor IN ('FULLCARGA_CASHIN', 'FULLCARGA_CASHOUT')
        ORDER BY created_at
    """)
    print(df)
except duckdb.IOException as e:
    print("Error de DuckDB en CASHIN_PROD:", e)

# **7. Examinar la estructura de trama_entrada para todas las transacciones**
try:
    print("\nExaminando trama_entrada para todas las transacciones...")
    df = duckdb.sql(f"""
        SELECT id_transaccion, recaudador, trama_entrada
        FROM read_parquet('{pre_conciliacion_path}')
        WHERE id_transaccion IN ('5000024037', '5000024052', '8000024032', '5000024071', '9000023988', '7000023969', '8000024102')
    """)
    print(df)

    # Mostrar el contenido completo de trama_entrada para cada transacción
    if len(df) > 0:
        # Convertir a pandas DataFrame
        pdf = df.fetchdf()
        for i in range(len(pdf)):
            id_tx = pdf.iloc[i]['id_transaccion']
            recaudador = pdf.iloc[i]['recaudador']
            trama = pdf.iloc[i]['trama_entrada']
            print(f"\nContenido de trama_entrada para {id_tx} ({recaudador}):")
            print(trama)

            # Intentar extraer el monto usando diferentes métodos
            import re
            import json

            # Método 1: Convertir a diccionario y acceder directamente
            try:
                trama_dict = eval(trama)
                if isinstance(trama_dict, dict) and 'amount' in trama_dict:
                    print(f"Monto extraído (método diccionario): {trama_dict['amount']}")
            except:
                pass

            # Método 2: Usar expresiones regulares para extraer monto
            try:
                match = re.search(r"'amount':\s*'([0-9.]+)'", trama)
                if match:
                    print(f"Monto extraído (regex 1): {match.group(1)}")

                match = re.search(r'"amount":\s*"([0-9.]+)"', trama)
                if match:
                    print(f"Monto extraído (regex 2): {match.group(1)}")

                match = re.search(r"amount['\"]?:\s*['\"]?([0-9.]+)", trama)
                if match:
                    print(f"Monto extraído (regex 3): {match.group(1)}")
            except:
                pass

            # Método 3: Extraer external_transaction_id y external_id
            try:
                # Para FULLCARGA_CASHOUT, buscar external_transaction_id
                if recaudador == 'FULLCARGA_CASHOUT':
                    # Intentar extraer directamente del diccionario
                    trama_dict = eval(trama)
                    if 'external_transaction_id' in trama_dict:
                        print(f"external_transaction_id extraído (dict): {trama_dict['external_transaction_id']}")
                    else:
                        # Intentar con regex
                        match = re.search(r"'external_transaction_id':\s*'([0-9]+)'", trama)
                        if match:
                            print(f"external_transaction_id extraído (regex): {match.group(1)}")
                        else:
                            print("No se encontró external_transaction_id")

                # Para FULLCARGA_CASHIN, buscar external_id
                if recaudador == 'FULLCARGA_CASHIN':
                    # Intentar extraer directamente del diccionario
                    trama_dict = eval(trama)
                    if 'external_id' in trama_dict:
                        print(f"external_id extraído (dict): {trama_dict['external_id']}")
                    else:
                        # Intentar con regex
                        match = re.search(r"'external_id':\s*'([0-9]+)'", trama)
                        if match:
                            print(f"external_id extraído (regex): {match.group(1)}")
                        else:
                            print("No se encontró external_id")
            except Exception as e:
                print(f"Error al extraer external_id/external_transaction_id: {str(e)}")
except duckdb.IOException as e:
    print("Error al examinar trama_entrada:", e)


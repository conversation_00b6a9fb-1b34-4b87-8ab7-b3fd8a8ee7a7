#!/usr/bin/env python3
import duckdb
import os

def analyze_parquet(file_path, label):
    if not os.path.exists(file_path):
        print(f'{label}: ARCHIVO NO EXISTE')
        return
    
    conn = duckdb.connect()
    try:
        # Obtener información básica
        result = conn.execute(f"SELECT COUNT(*) as total_rows FROM '{file_path}'").fetchone()
        total_rows = result[0]
        
        # Obtener columnas
        columns_info = conn.execute(f"DESCRIBE SELECT * FROM '{file_path}'").fetchall()
        
        print(f'{label}:')
        print(f'  Total filas: {total_rows:,}')
        print(f'  Total columnas: {len(columns_info)}')
        print(f'  Columnas: {[col[0] for col in columns_info]}')
        print(f'  Tipos: {[col[1] for col in columns_info]}')
        
        # Mostrar algunas filas de muestra si hay datos
        if total_rows > 0:
            sample = conn.execute(f"SELECT * FROM '{file_path}' LIMIT 3").fetchall()
            print(f'  Muestra (primeras 3 filas): {sample}')
        else:
            print(f'  ARCHIVO VACÍO - NO HAY DATOS')
        print()
        
    except Exception as e:
        print(f'{label}: ERROR - {str(e)}')
        print()
    finally:
        conn.close()

# Archivos a comparar
files = [
    'USER_ACCOUNT_HISTORY.parquet',
    'USER_AUTH_CHANGE_HISTORY.parquet',
    'USER_DATA_TRX.parquet',
    'USER_MODIFICATION_DAY.parquet'
]

print('=== HOMOLOGACIÓN DE ARCHIVOS TEMPORALES ===')
print()

for file in files:
    original_path = f'S3_LOG_USER/TEMP_LOGS_USUARIOS/********/{file}'
    logica_path = f'LOGICA/TEMP_LOGS_USUARIOS/********/{file}'

    print(f'--- {file} ---')
    analyze_parquet(original_path, 'ORIGINAL')
    analyze_parquet(logica_path, 'LOGICA')

    # Comparación de homologación
    if os.path.exists(original_path) and os.path.exists(logica_path):
        try:
            conn = duckdb.connect()
            original_count = conn.execute(f"SELECT COUNT(*) FROM '{original_path}'").fetchone()[0]
            logica_count = conn.execute(f"SELECT COUNT(*) FROM '{logica_path}'").fetchone()[0]

            original_cols = len(conn.execute(f"DESCRIBE SELECT * FROM '{original_path}'").fetchall())
            logica_cols = len(conn.execute(f"DESCRIBE SELECT * FROM '{logica_path}'").fetchall())

            if original_count == logica_count and original_cols == logica_cols:
                print(f"✅ HOMOLOGACIÓN PERFECTA: {original_count:,} filas, {original_cols} columnas")
            else:
                print(f"❌ DIFERENCIAS DETECTADAS:")
                print(f"   Filas: Original={original_count:,}, LOGICA={logica_count:,}")
                print(f"   Columnas: Original={original_cols}, LOGICA={logica_cols}")
            conn.close()
        except Exception as e:
            print(f"⚠️ Error comparando: {e}")

    print('=' * 50)
    print()

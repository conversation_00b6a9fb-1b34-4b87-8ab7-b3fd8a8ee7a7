# Configuración del pipeline de procesamiento
pipeline:
  max_retries: 3
  timeout_seconds: 1500
  log_level: "INFO"
  
# Configuración de MySQL (para extracción directa si es necesaria)
mysql:
  host: "${MYSQL_HOST}"
  port: 3306
  database: "app_bim_prod_1"
  user: "${MYSQL_USER}"
  password: "${MYSQL_PASSWORD}"
  
# Configuración de AWS
aws:
  region: "us-east-1"
  profile: "${AWS_PROFILE}"
  
# Configuración de procesamiento
processing:
  chunk_size: 10000
  memory_limit_gb: 8
  use_polars: true  # true para usar polars, false para pandas
  
# Configuración de logging
logging:
  log_dir: "logs"
  log_file_pattern: "{process}_{date}.log"
  max_log_files: 30

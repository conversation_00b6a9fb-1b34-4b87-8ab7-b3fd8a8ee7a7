#!/usr/bin/env python3
"""
Script genérico para ejecutar reportes ETL configurables.
<PERSON> datos desde S3, los procesa mediante consultas SQL y los exporta a archivos Parquet.

Uso:
    python run_reporte.py NOMBRE_REPORTE

Ejemplo:
    python run_reporte.py REPORTE_FULLCARGA
"""
import os
import sys
import configparser
import logging
import duckdb
import boto3

import subprocess
import tempfile
from datetime import datetime, timedelta

def main():
    # Verificar argumentos
    if len(sys.argv) != 2:
        print("Uso: python run_reporte.py NOMBRE_REPORTE")
        print("Ejemplo: python run_reporte.py REPORTE_FULLCARGA")
        sys.exit(1)

    # Obtener nombre del reporte
    nombre_reporte = sys.argv[1]

    # Cargar configuración desde S3
    config = load_config_from_s3(nombre_reporte)
    if config is None:
        print(f"Error: No se pudo cargar la configuración para {nombre_reporte}")
        sys.exit(1)

    # Las queries ahora se cargan desde S3, no necesitamos verificar directorio local

    # La configuración ya está cargada desde S3

    # Cargar configuración S3 para logs
    s3_config = load_s3_log_config()

    # Configurar logging
    log_file = setup_logging(nombre_reporte, config, s3_config)

    try:
        # Determinar rango de fechas
        fecha_inicio, fecha_fin = get_date_range(config)
        logging.info(f"Procesando datos para {nombre_reporte} desde {fecha_inicio} hasta {fecha_fin}")

        # Procesar cada fecha en el rango
        current_date = fecha_inicio
        while current_date <= fecha_fin:
            fecha_str = current_date.strftime('%Y-%m-%d')
            process_date(fecha_str, config, nombre_reporte)
            current_date += timedelta(days=1)

        logging.info("Proceso completado exitosamente")

    except Exception as e:
        logging.error(f"Error durante la ejecución: {str(e)}")
        raise
    finally:
        # Cerrar todos los handlers de logging antes de subir el archivo
        for handler in logging.getLogger().handlers[:]:
            handler.close()
            logging.getLogger().removeHandler(handler)
        
        # Subir log a S3 (obligatorio)
        if s3_config and s3_config.get('enabled'):
            final_log_location = upload_log_to_s3(log_file, nombre_reporte, s3_config)
            print(f"📍 Ubicación final del log: {final_log_location}")
        else:
            print(f"❌ Error: No se pudo procesar el log - configuración S3 no válida")
            print(f"🗑️ Eliminando archivo temporal: {log_file}")
            try:
                if os.path.exists(log_file):
                    os.remove(log_file)
            except Exception as e:
                print(f"⚠️ No se pudo eliminar el archivo temporal: {e}")

def load_config_from_s3(nombre_reporte):
    """Carga la configuración del reporte desde S3"""
    try:
        # Cargar configuración S3 para reportes
        import configparser
        import boto3
        import tempfile

        s3_config = configparser.ConfigParser()
        s3_config.read('config/s3_report_config.ini')

        if not (s3_config.has_section('CONFIGS_REPORTES') and s3_config.has_option('CONFIGS_REPORTES', 'bucket')):
            print("❌ Error: Configuración S3 para reportes no encontrada")
            return None

        s3_bucket = s3_config.get('CONFIGS_REPORTES', 'bucket')
        config_s3_path = f"CONFIGURACIONES/{nombre_reporte}/config.ini"

        print(f"📊 Cargando configuración desde S3: s3://{s3_bucket}/{config_s3_path}")

        # Descargar archivo de configuración desde S3
        s3_client = boto3.client('s3')

        # Crear archivo temporal
        with tempfile.NamedTemporaryFile(mode='w+', suffix='.ini', delete=False) as temp_file:
            temp_config_path = temp_file.name

        # Descargar desde S3
        s3_client.download_file(s3_bucket, config_s3_path, temp_config_path)

        # Cargar configuración
        config = configparser.ConfigParser()
        config.read(temp_config_path)

        # Limpiar archivo temporal
        os.unlink(temp_config_path)

        print(f"✅ Configuración cargada desde S3 exitosamente")
        return config

    except Exception as e:
        print(f"❌ Error cargando configuración desde S3: {e}")
        print(f"🔄 Intentando fallback a archivo local...")

        # Fallback a archivo local
        try:
            config_path = os.path.join("CONFIGURACIONES", nombre_reporte, "config.ini")
            if os.path.exists(config_path):
                config = configparser.ConfigParser()
                config.read(config_path)
                print(f"✅ Configuración cargada desde archivo local: {config_path}")
                return config
            else:
                print(f"❌ Archivo local tampoco encontrado: {config_path}")
                return None
        except Exception as fallback_error:
            print(f"❌ Error en fallback local: {fallback_error}")
            return None

def load_query_from_s3(nombre_reporte, query_name):
    """Carga una query SQL específica desde S3"""
    try:
        # Cargar configuración S3 para reportes
        import configparser
        import boto3
        import tempfile

        s3_config = configparser.ConfigParser()
        s3_config.read('config/s3_report_config.ini')

        if not (s3_config.has_section('CONFIGS_REPORTES') and s3_config.has_option('CONFIGS_REPORTES', 'bucket')):
            logging.error("❌ Error: Configuración S3 para reportes no encontrada")
            return None

        s3_bucket = s3_config.get('CONFIGS_REPORTES', 'bucket')
        query_s3_path = f"REPORTES/{nombre_reporte}/queries/{query_name}.sql"

        logging.info(f"📊 Cargando query desde S3: s3://{s3_bucket}/{query_s3_path}")

        # Descargar archivo de query desde S3
        s3_client = boto3.client('s3')

        # Crear archivo temporal
        with tempfile.NamedTemporaryFile(mode='w+', suffix='.sql', delete=False) as temp_file:
            temp_query_path = temp_file.name

        # Descargar desde S3
        s3_client.download_file(s3_bucket, query_s3_path, temp_query_path)

        # Leer contenido de la query
        with open(temp_query_path, 'r') as f:
            query_content = f.read()

        # Limpiar archivo temporal
        os.unlink(temp_query_path)

        logging.info(f"✅ Query cargada desde S3 exitosamente: {query_name}.sql")
        return query_content

    except Exception as e:
        logging.error(f"❌ Error cargando query desde S3: {e}")
        logging.info(f"🔄 Intentando fallback a archivo local...")

        # Fallback a archivo local
        try:
            queries_dir = os.path.join("REPORTES", nombre_reporte, "queries")
            query_file = os.path.join(queries_dir, f"{query_name}.sql")
            if os.path.exists(query_file):
                with open(query_file, 'r') as f:
                    query_content = f.read()
                logging.info(f"✅ Query cargada desde archivo local: {query_file}")
                return query_content
            else:
                logging.error(f"❌ Archivo local tampoco encontrado: {query_file}")
                return None
        except Exception as fallback_error:
            logging.error(f"❌ Error en fallback local: {fallback_error}")
            return None

def load_s3_log_config():
    """Carga la configuración de logs S3 - REQUERIDA para el funcionamiento"""
    try:
        s3_config_path = os.path.join("config", "s3_log_config.ini")
        if not os.path.exists(s3_config_path):
            print(f"❌ Error: No se encontró {s3_config_path}. Configuración S3 es requerida para logs.")
            return None
            
        s3_config = configparser.ConfigParser()
        s3_config.read(s3_config_path)
        
        if s3_config.has_section('CONFIGS_LOG') and s3_config.has_option('CONFIGS_LOG', 'bucket'):
            bucket = s3_config.get('CONFIGS_LOG', 'bucket')
            if bucket.strip():
                return {
                    'bucket': bucket,
                    'enabled': True
                }
            else:
                print("❌ Error: El bucket S3 está vacío en la configuración.")
                return None
        else:
            print("❌ Error: Configuración S3 incompleta. Sección [CONFIGS_LOG] o 'bucket' no encontrados.")
            return None
            
    except Exception as e:
        print(f"❌ Error cargando configuración S3: {e}")
        return None

def upload_log_to_s3(log_file_path, nombre_reporte, s3_config):
    """Sube el archivo de log a S3 y elimina la copia local"""
    try:
        if not s3_config or not s3_config.get('enabled'):
            print(f"❌ Error: No hay configuración S3 válida para subir logs")
            print(f"📁 El log temporal se mantiene en: {log_file_path}")
            return log_file_path
            
        # Verificar que el archivo de log existe
        if not os.path.exists(log_file_path):
            print(f"❌ Error: El archivo de log no existe: {log_file_path}")
            return log_file_path
            
        # Crear cliente S3
        s3_client = boto3.client('s3')
        
        # Generar nombre de archivo con formato estándar
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"{nombre_reporte}_{timestamp}.log"
        
        # Construir la ruta S3 con la estructura: {NOMBRE_REPORTE}/{archivo.log}
        s3_key = f"{nombre_reporte}/{log_filename}"
        
        # Verificar el tamaño del archivo
        file_size = os.path.getsize(log_file_path)
        print(f"📤 Subiendo log a S3 ({file_size} bytes): s3://{s3_config['bucket']}/{s3_key}")
        
        # Subir archivo a S3
        s3_client.upload_file(
            log_file_path, 
            s3_config['bucket'], 
            s3_key,
            ExtraArgs={
                'ContentType': 'text/plain',
                'Metadata': {
                    'reporte': nombre_reporte,
                    'timestamp': datetime.now().isoformat()
                }
            }
        )
        
        # Verificar que el archivo se subió correctamente
        try:
            s3_client.head_object(Bucket=s3_config['bucket'], Key=s3_key)
            print(f"✅ Log subido exitosamente a S3")
        except Exception as verify_error:
            print(f"⚠️ Advertencia: No se pudo verificar la subida: {verify_error}")
            return log_file_path
        
        # Eliminar archivo temporal después de subida exitosa
        try:
            if os.path.exists(log_file_path):
                os.remove(log_file_path)
                print(f"🗑️ Archivo temporal eliminado")
        except Exception as delete_error:
            print(f"⚠️ Advertencia: No se pudo eliminar el archivo temporal: {delete_error}")
            
        return f"s3://{s3_config['bucket']}/{s3_key}"
        
    except Exception as e:
        print(f"❌ Error subiendo log a S3: {e}")
        print(f"📁 El log temporal se mantiene en: {log_file_path}")
        return log_file_path

def setup_logging(nombre_reporte, config, s3_config=None):
    """Configura el sistema de logging con almacenamiento directo en S3"""
    # Cargar configuración S3 si no se proporcionó
    if s3_config is None:
        s3_config = load_s3_log_config()
    
    # Configurar el formato del log
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Crear archivo temporal para el log (se subirá a S3 y luego se eliminará)
    log_file = tempfile.NamedTemporaryFile(
        mode='w',
        prefix=f'{nombre_reporte}_{timestamp}_',
        suffix='.log',
        delete=False  # No eliminar automáticamente, lo haremos manualmente después de subirlo
    ).name
    
    if s3_config and s3_config.get('enabled'):
        print(f"📋 Configuración S3 activa. Los logs se subirán a s3://{s3_config['bucket']}/{nombre_reporte}/")
    else:
        print(f"⚠️ Advertencia: No hay configuración S3 válida. El log temporal se mantendrá en: {log_file}")

    # Configurar el logger
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # También mostrar en consola
        ]
    )

    return log_file

def get_date_range(config):
    """Determina el rango de fechas a procesar según la configuración"""
    # Leer el valor del switch de rango
    usar_rango_fijo = config.getboolean('fecha', 'rango', fallback=False)

    if usar_rango_fijo:
        # Usar rango fijo
        return (
            datetime.strptime(config.get('fecha', 'rango_inicio'), '%Y-%m-%d').date(),
            datetime.strptime(config.get('fecha', 'rango_fin'), '%Y-%m-%d').date()
        )
    else:
        # Usar modo relativo
        dias_atras = int(config.get('fecha', 'dias_atras', fallback='1'))
        today = datetime.now().date()
        return (today - timedelta(days=dias_atras), today - timedelta(days=1))

def run_post_processing_scripts(config, fecha, s3_output_path):
    """Ejecuta los scripts de post-procesamiento configurados"""
    # Verificar si existe la sección post_processing
    if not config.has_section('post_processing'):
        logging.info("No se encontró la sección [post_processing] en el archivo de configuración. No se ejecutarán scripts de post-procesamiento.")
        return

    # Verificar si hay scripts configurados
    if not config.has_option('post_processing', 'scripts'):
        logging.info("No hay scripts de post-procesamiento configurados.")
        return

    # Obtener la lista de scripts
    scripts_config = config.get('post_processing', 'scripts', fallback='')
    if not scripts_config.strip():
        logging.info("No hay scripts de post-procesamiento configurados.")
        return

    # Procesar cada script
    scripts = [s.strip() for s in scripts_config.split(',')]
    for script_path in scripts:
        if not script_path:
            continue

        # Descargar script desde S3 temporalmente y ejecutar
        logging.info(f"Ejecutando script de post-procesamiento desde S3: {script_path} {fecha}")
        try:
            # Cargar configuración S3 para reportes
            import configparser
            import boto3
            import tempfile

            s3_config = configparser.ConfigParser()
            s3_config.read('config/s3_report_config.ini')

            if not (s3_config.has_section('CONFIGS_REPORTES') and s3_config.has_option('CONFIGS_REPORTES', 'bucket')):
                logging.error("❌ Error: Configuración S3 para reportes no encontrada")
                continue

            s3_bucket = s3_config.get('CONFIGS_REPORTES', 'bucket')
            script_s3_path = script_path  # Ya viene con la ruta completa REPORTES/...

            logging.info(f"📊 Descargando script desde S3: s3://{s3_bucket}/{script_s3_path}")

            # Descargar script desde S3 temporalmente
            s3_client = boto3.client('s3')

            # Crear archivo temporal
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.py', delete=False) as temp_file:
                temp_script_path = temp_file.name

            # Descargar desde S3
            s3_client.download_file(s3_bucket, script_s3_path, temp_script_path)

            logging.info(f"✅ Script descargado temporalmente para ejecución")

            # Construir el comando con el archivo temporal
            command = [sys.executable, temp_script_path, fecha]

            # Ejecutar el script
            result = subprocess.run(command, capture_output=True, text=True)

            # Limpiar archivo temporal
            os.unlink(temp_script_path)

            if result.returncode == 0:
                logging.info(f"✅ Script de post-procesamiento ejecutado con éxito desde S3: {script_path}")
                if result.stdout:
                    logging.info(f"Salida del script:\n{result.stdout}")
            else:
                logging.error(f"❌ Error al ejecutar el script de post-procesamiento: {script_path}")
                if result.stderr:
                    logging.error(f"Error: {result.stderr}")
                if result.stdout:
                    logging.info(f"Salida: {result.stdout}")

        except Exception as e:
            logging.error(f"❌ Error al ejecutar el script de post-procesamiento {script_path}: {str(e)}")
            # Limpiar archivo temporal si existe
            try:
                if 'temp_script_path' in locals():
                    os.unlink(temp_script_path)
            except:
                pass

def process_date(fecha, config, nombre_reporte):
    """Procesa los datos para una fecha específica"""
    logging.info(f"Procesando fecha: {fecha}")

    # Extraer componentes de la fecha
    fecha_parts = fecha.split('-')
    year, month, day = fecha_parts[0], fecha_parts[1], fecha_parts[2]

    # Configurar conexión a DuckDB
    conn = duckdb.connect(database=':memory:')

    # Obtener credenciales activas de AWS CLI
    session = boto3.Session()
    credentials = session.get_credentials().get_frozen_credentials()

    # Cargar soporte para S3 en DuckDB
    conn.sql("INSTALL httpfs;")
    conn.sql("LOAD httpfs;")
    conn.sql("SET s3_region='us-east-1';")
    conn.sql("SET s3_use_ssl=true;")
    conn.sql("SET s3_url_style='path';")

    # Pasar credenciales explícitamente
    conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
    conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
    conn.sql(f"SET s3_session_token='{credentials.token}';")

    # Construir las rutas S3 para todos los orígenes definidos
    s3_paths = {}

    # Verificar que exista la sección s3_sources
    if not config.has_section('s3_sources'):
        logging.error("Error: No se encontró la sección [s3_sources] en el archivo de configuración")
        raise ValueError("La sección [s3_sources] es obligatoria en el archivo de configuración")

    # Procesar cada fuente definida en la sección s3_sources
    for source_name, source_value in config.items('s3_sources'):
        parts = [p.strip() for p in source_value.split(',')]
        if len(parts) >= 2:
            bucket = parts[0]
            prefix = parts[1]
            region = parts[2] if len(parts) > 2 else 'us-east-1'

            # Si el prefix termina en .parquet, no agregar estructura de fechas
            if prefix.endswith('.parquet'):
                path = f's3://{bucket}/{prefix}'
            else:
                path = f's3://{bucket}/{prefix}/{year}/{month}/{day}/*'

            s3_paths[source_name] = {
                'bucket': bucket,
                'prefix': prefix,
                'region': region,
                'path': path
            }

    # Construir la ruta de destino para el archivo Parquet
    output_base_dir = config.get('general', 'output_dir')
    output_dir = f'{output_base_dir}/{year}/{month}/{day}'
    output_file = f'{output_dir}/{nombre_reporte}_{fecha}.parquet'

    # No crear la carpeta hasta que sepamos que hay registros para procesar

    try:
        # Obtener lista de queries a ejecutar
        query_list = config.get('queries', 'query_list', fallback='').split(',')
        query_list = [q.strip() for q in query_list if q.strip()]

        if not query_list:
            logging.error("No se especificaron queries para ejecutar")
            return

        # Procesar cada query
        for query_name in query_list:
            # Cargar query desde S3
            query_template = load_query_from_s3(nombre_reporte, query_name)

            if query_template is None:
                logging.error(f"No se pudo cargar la query: {query_name}.sql")
                continue

            # Calcular el día siguiente para buscar transacciones reversadas
            fecha_dt = datetime.strptime(fecha, '%Y-%m-%d')
            next_date = fecha_dt + timedelta(days=1)
            next_year, next_month, next_day = next_date.strftime('%Y'), next_date.strftime('%m'), next_date.strftime('%d')

            # Crear un diccionario con todos los parámetros para formatear la query
            format_params = {
                'year': year,
                'month': month,
                'day': day,
                'next_year': next_year,
                'next_month': next_month,
                'next_day': next_day,
                'fecha_inicio': fecha,
                'fecha_fin': fecha
            }

            # TODO EL ERSTO DE LA TABLAS {nombre_fuente}_bucket
            # Agregar todos los paths S3 como parámetros
            # Para cada fuente definida en config.ini, generamos variables como:
            # {nombre_fuente}_bucket, {nombre_fuente}_prefix, {nombre_fuente}_path, etc.
            # Estas variables pueden usarse en los archivos SQL
            for source_name, source_info in s3_paths.items():
                format_params[f'{source_name}_bucket'] = source_info['bucket']
                format_params[f'{source_name}_prefix'] = source_info['prefix']
                format_params[f'{source_name}_region'] = source_info['region']
                format_params[f'{source_name}_path'] = source_info['path']

                # Ejemplo: Si source_name es 'cashin_prod', se generan:
                # - cashin_prod_bucket
                # - cashin_prod_prefix
                # - cashin_prod_region
                # - cashin_prod_path (ruta completa)

            # Reemplazar parámetros en la query
            # Primero, escapar las llaves que no son parte del formato
            query_template = query_template.replace('{{', '{{{{').replace('}}', '}}}}')
            
            # Debug: mostrar variables disponibles
            logging.info(f"Variables disponibles para reemplazo: {list(format_params.keys())}")
            
            query = query_template.format(**format_params)

            # Ejecutar la query
            logging.info(f"Ejecutando query: {query_name}")
            try:
                # Intentar ejecutar la query original primero
                df = conn.sql(query).fetchdf()
                logging.info(f"Se encontraron {len(df)} registros para procesar")
            except Exception as query_error:
                error_str = str(query_error)
                logging.warning(f"Error al ejecutar la query original: {error_str}")

                # Aplicar una solución que preserve todos los datos tratándolos como texto
                logging.info("Aplicando solución para manejar JSON como texto (VARCHAR)...")

                # Modificar la query para tratar los campos JSON como texto
                query_modified = query

                # Modificar la sección USER_MODIFICATION_DAY para tratar OLD_DATA y NEW_DATA como texto
                if "WHEN TRY_CAST(UMH.OLD_DATA AS JSON) IS NOT NULL THEN UMH.OLD_DATA" in query_modified:
                    query_modified = query_modified.replace(
                        "CASE\n            WHEN TRY_CAST(UMH.OLD_DATA AS JSON) IS NOT NULL THEN UMH.OLD_DATA\n            ELSE NULL\n        END AS OLD_DATA",
                        "UMH.OLD_DATA AS OLD_DATA"
                    )

                if "WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL THEN UMH.NEW_DATA" in query_modified:
                    query_modified = query_modified.replace(
                        "CASE\n            WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL THEN UMH.NEW_DATA\n            ELSE NULL\n        END AS NEW_DATA",
                        "UMH.NEW_DATA AS NEW_DATA"
                    )

                # Modificar la sección final para tratar oldData y newData como texto
                if "WHEN TRY_CAST(P.oldData AS JSON) IS NOT NULL THEN P.oldData" in query_modified:
                    query_modified = query_modified.replace(
                        "CASE\n        WHEN TRY_CAST(P.oldData AS JSON) IS NOT NULL THEN P.oldData\n        ELSE NULL\n    END AS oldData",
                        "P.oldData AS oldData"
                    )

                if "WHEN TRY_CAST(P.newData AS JSON) IS NOT NULL THEN P.newData" in query_modified:
                    query_modified = query_modified.replace(
                        "CASE\n        WHEN TRY_CAST(P.newData AS JSON) IS NOT NULL THEN P.newData\n        ELSE NULL\n    END AS newData",
                        "P.newData AS newData"
                    )

                # Modificar la sección que extrae RAZON para usar CAST en lugar de TRY_CAST
                if "WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL AND TRY_CAST(JSON_EXTRACT" in query_modified:
                    # Buscar la sección CASE completa para RAZON
                    razon_case_start = query_modified.find("CASE\n            -- Solo intentar extraer JSON si el campo es un JSON válido")
                    if razon_case_start >= 0:
                        razon_case_end = query_modified.find("END AS RAZON", razon_case_start)
                        if razon_case_end >= 0:
                            razon_case_end += len("END AS RAZON")
                            razon_case = query_modified[razon_case_start:razon_case_end]

                            # Crear una nueva sección CASE para RAZON que maneje el caso especial
                            new_razon_case = """CASE
            -- Intentar extraer RAZON de manera segura
            WHEN UMH.NEW_DATA = 'CIERRE POR APP BIM' THEN 'CIERRE POR APP BIM'
            WHEN UMH.NEW_DATA LIKE '%CIERRE POR APP BIM%' THEN 'CIERRE POR APP BIM'
            WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL THEN
                COALESCE(
                    TRY_CAST(JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails.remarks') AS VARCHAR),
                    TRY_CAST(JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails[0].remarks') AS VARCHAR)
                )
            ELSE NULL
        END AS RAZON"""

                            # Reemplazar la sección CASE original con la nueva
                            query_modified = query_modified.replace(razon_case, new_razon_case)

                # Si hay problemas con LIKE en JSON, convertir a VARCHAR
                if "WHEN UMH.NEW_DATA LIKE '%CIERRE POR APP BIM%'" in query_modified:
                    query_modified = query_modified.replace(
                        "WHEN UMH.NEW_DATA LIKE '%CIERRE POR APP BIM%'",
                        "WHEN CAST(UMH.NEW_DATA AS VARCHAR) LIKE '%CIERRE POR APP BIM%'"
                    )

                try:
                    df = conn.sql(query_modified).fetchdf()
                    logging.info(f"Solución para tratar JSON como texto aplicada con éxito. Se encontraron {len(df)} registros para procesar")
                except Exception as e:
                    logging.error(f"Error al aplicar la solución para tratar JSON como texto: {str(e)}")

                    # Si la solución anterior falla, intentar una solución más simple
                    logging.warning("Intentando solución alternativa...")

                    # Modificar la query de manera más simple
                    query_simple = query

                    # Reemplazar todas las validaciones de JSON con conversiones directas a VARCHAR
                    query_simple = query_simple.replace("TRY_CAST(UMH.OLD_DATA AS JSON)", "CAST(UMH.OLD_DATA AS VARCHAR)")
                    query_simple = query_simple.replace("TRY_CAST(UMH.NEW_DATA AS JSON)", "CAST(UMH.NEW_DATA AS VARCHAR)")
                    query_simple = query_simple.replace("TRY_CAST(P.oldData AS JSON)", "CAST(P.oldData AS VARCHAR)")
                    query_simple = query_simple.replace("TRY_CAST(P.newData AS JSON)", "CAST(P.newData AS VARCHAR)")

                    # Reemplazar JSON_EXTRACT con NULL para evitar errores
                    query_simple = query_simple.replace("JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails.remarks')", "NULL")
                    query_simple = query_simple.replace("JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails[0].remarks')", "NULL")

                    try:
                        df = conn.sql(query_simple).fetchdf()
                        logging.info(f"Solución alternativa aplicada con éxito. Se encontraron {len(df)} registros para procesar")
                    except Exception as e2:
                        logging.error(f"Error al aplicar la solución alternativa: {str(e2)}")

                        # Si todo lo demás falla, usar la solución de emergencia original
                        logging.warning("Aplicando solución de emergencia (reemplazar operaciones JSON con NULL)...")

                        # Modificar la query para evitar problemas de JSON
                        query_emergency = query

                        # Reemplazar todas las operaciones TRY_CAST con NULL
                        query_emergency = query_emergency.replace("TRY_CAST(P.newData AS JSON)", "NULL")
                        query_emergency = query_emergency.replace("TRY_CAST(P.oldData AS JSON)", "NULL")
                        query_emergency = query_emergency.replace("TRY_CAST(UMH.NEW_DATA AS JSON)", "NULL")
                        query_emergency = query_emergency.replace("TRY_CAST(UMH.OLD_DATA AS JSON)", "NULL")

                        # Reemplazar JSON_EXTRACT con NULL
                        query_emergency = query_emergency.replace("JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails.remarks')", "NULL")
                        query_emergency = query_emergency.replace("JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails[0].remarks')", "NULL")

                        try:
                            df = conn.sql(query_emergency).fetchdf()
                            logging.info(f"Solución de emergencia aplicada. Se encontraron {len(df)} registros para procesar")
                        except Exception as emergency_error:
                            logging.error(f"Error al aplicar solución de emergencia: {str(emergency_error)}")
                            raise

            if len(df) == 0:
                logging.warning(f"No hay registros para procesar en la fecha {fecha}")
                continue

            # Exportar DIRECTAMENTE a S3 (sin archivos locales)
            conn.sql(f"""
                CREATE TABLE temp_data AS SELECT * FROM df
            """)

            # Guardar SOLO en S3 usando configuración Golden
            try:
                import configparser
                config_s3 = configparser.ConfigParser()
                config_s3.read('config/s3_golden_config.ini')

                if config_s3.has_section('CONFIGS_GOLDEN') and config_s3.has_option('CONFIGS_GOLDEN', 'bucket'):
                    s3_bucket = config_s3.get('CONFIGS_GOLDEN', 'bucket')

                    # Construir ruta S3 manteniendo la misma estructura
                    s3_output_path = f"s3://{s3_bucket}/{output_file}"

                    # Guardar DIRECTAMENTE en S3
                    conn.sql(f"""
                        COPY (SELECT * FROM temp_data)
                        TO '{s3_output_path}' (FORMAT PARQUET)
                    """)

                    logging.info(f"✅ Resultados exportados DIRECTAMENTE a S3: {s3_output_path}")
                else:
                    logging.error("❌ Error: Configuración S3 Golden no encontrada - NO se puede proceder sin S3")
                    raise ValueError("Configuración S3 Golden es obligatoria")

            except Exception as e:
                logging.error(f"❌ Error guardando en S3: {e}")
                raise

            # Mostrar muestra de datos
            muestra_df = conn.sql("SELECT * FROM temp_data LIMIT 5").fetchdf()
            logging.info(f"Muestra de datos procesados:\n{muestra_df}")

            # Limpiar tabla temporal
            conn.sql("DROP TABLE IF EXISTS temp_data")

            # Ejecutar scripts de post-procesamiento si están configurados
            # Nota: Los scripts ahora trabajan directamente con S3, no necesitan archivos locales
            run_post_processing_scripts(config, fecha, s3_output_path)

    except Exception as e:
        logging.error(f"Error al procesar la fecha {fecha}: {str(e)}")

if __name__ == "__main__":
    main()
